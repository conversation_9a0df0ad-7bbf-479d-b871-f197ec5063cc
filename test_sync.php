<!DOCTYPE html>
<html>
<head>
    <title>Employee Sync Test</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <h1>Employee Sync Test</h1>
    <button id="syncBtn">Sync Employees Now</button>
    <button id="checkUsersBtn">Check Users Count</button>
    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        const baseUrl = '<?php echo base_url(); ?>';
        
        $('#syncBtn').click(function() {
            $('#results').html('Syncing employees...');
            
            $.ajax({
                url: baseUrl + 'EmployeeController/syncEmployees',
                method: 'GET',
                success: function(response) {
                    $('#results').html('<strong>Sync Response:</strong><br>' + JSON.stringify(response));
                    console.log("Sync Complete:", response);
                },
                error: function(xhr, status, error) {
                    $('#results').html('<strong>Sync Error:</strong><br>' + error + '<br><strong>Status:</strong> ' + status + '<br><strong>Response:</strong> ' + xhr.responseText);
                    console.error("Sync Error:", error);
                }
            });
        });
        
        $('#checkUsersBtn').click(function() {
            // Simple way to check users count - you might need to create this endpoint
            $.ajax({
                url: baseUrl + 'EmployeeController/getUsersCount',
                method: 'GET',
                success: function(response) {
                    $('#results').html('<strong>Users Count:</strong><br>' + response);
                },
                error: function(xhr, status, error) {
                    $('#results').html('<strong>Error checking users:</strong><br>' + error);
                }
            });
        });
    </script>
</body>
</html>
