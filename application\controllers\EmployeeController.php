<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EmployeeController extends CI_Controller
{

   public function __construct()
   {
      parent::__construct();
   }

   public function showEmployeePage()
   {
      $this->fetchEmployees();
   }

   private function fetchEmployees()
   {
      $url = $_ENV['API_EMPLOYEES_URL'] ?? 'http://************:3001/api/services/app/HR201EmployeesV2/GetAllV2';

      $curl = curl_init($url);
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($curl, CURLOPT_HTTPHEADER, array(
         'Accept: application/json'
      ));

      $response = curl_exec($curl);
      if (curl_errno($curl)) {
         echo 'cURL Error: ' . curl_error($curl);
         curl_close($curl);
         return;
      }
      curl_close($curl);

      // Decode and handle the employee data
      $result = json_decode($response, true);

      echo '<pre>';

      // Initialize arrays to categorize employees
      $with_business_unit = [];
      $without_business_unit = [];
      $others = [];

      // Create an array to track email occurrences
      $email_groups = [];

      // Loop through the employees and categorize them
      foreach ($result['result'] as $index => $employee) {
         $business_unit = $employee['hR201BusinessUnitDisplayProperty'] ?? null;
         $personal_email = $employee['hR201Employee']['personalEmailAddress'] ?? null;
         $employee_name = $employee['hR201Employee']['firstname'] . ' ' . $employee['hR201Employee']['lastname'];

         // Check for valid business unit (non-empty and not just spaces/dashes)
         if ($personal_email) {
            // Group employees by email
            if (!isset($email_groups[$personal_email])) {
               $email_groups[$personal_email] = [];
            }
            $email_groups[$personal_email][] = $employee_name;
         }

         if ($business_unit && !preg_match('/^\s*-+\s*$/', $business_unit)) {
            // Employees with a non-empty and valid 'hR201BusinessUnitDisplayProperty'
            $with_business_unit[] = "Employee " . ($index + 1) . ": " . $employee_name . ' (' . $business_unit . ')';
         } elseif (empty($business_unit)) {
            // Employees with an empty or null 'hR201BusinessUnitDisplayProperty'
            $without_business_unit[] = "Employee " . ($index + 1) . ": " . $employee_name;
         } else {
            // For other cases, such as business unit containing only dashes or spaces
            $others[] = "Employee " . ($index + 1) . ": " . $employee_name . ' (' . $business_unit . ')';
         }
      }

      // Display categorized lists with counts
      echo "Employees with valid business unit (non-empty, non-dash): " . count($with_business_unit) . " <br>";
      echo "<ul>";
      foreach ($with_business_unit as $employee) {
         echo "<li>{$employee}</li>";
      }
      echo "</ul>";

      echo "Employees without business unit (empty or null): " . count($without_business_unit) . " <br>";
      echo "<ul>";
      foreach ($without_business_unit as $employee) {
         echo "<li>{$employee}</li>";
      }
      echo "</ul>";

      echo "Employees with invalid business unit values (dashes, spaces, etc.): " . count($others) . " <br>";
      echo "<ul>";
      foreach ($others as $employee) {
         echo "<li>{$employee}</li>";
      }
      echo "</ul>";

      echo "Employees grouped by email: <br>";
      foreach ($email_groups as $email => $employees) {
         echo "Email: {$email} <br>";
         echo "Employees: " . implode(", ", $employees) . " <br><br>";
      }

      echo '</pre>';

      // Proceed with loading the view if there are employees
      if (isset($result['result'])) {
         $data['employees'] = $result['result'];
         $this->load->view('employee_view', $data);
      } else {
         echo "Failed to fetch employee data. Response: " . print_r($result, true);
      }
   }





   public function insertEmployees()
   {
      $employees = $this->input->post('employees');

      if (!empty($employees)) {
         foreach ($employees as $employee) {
            $email = $employee['email'] ?? null;

            // Generate a placeholder email if it's missing or N/A
            if (!$email || strtolower($email) == 'n/a') {
               $email = strtolower("placeholder_" . $employee['first_name'] . "_" . $employee['last_name'] . "@example.com");
            }

            if (!$this->User_model->check_email_exists($email)) {
               $data = [
                  'first_name'        => $employee['first_name'] ?? 'Unknown',
                  'last_name'         => $employee['last_name'] ?? 'Unknown',
                  'email'             => $email,
                  'department'        => $employee['department'] ?? 'None',
                  'role'              => $employee['role'] ?? 'N/A',
                  'admin_role'        => '',
                  'employee_status'   => $employee['employment_status'] ?? 'N/A',
                  'image_url'         => '',
                  'created_at'        => date('Y-m-d H:i:s'),
                  'last_active'       => '',
                  'employment_status' => $employee['employment_status'] ?? 'N/A',
                  'transfer_status'   => $employee['transfer_status'] ?? '0',
                  'contract_type'     => $employee['contract_type'] ?? 'N/A'
               ];

               $this->User_model->insert_user($data);
            }
         }

         echo json_encode(['status' => 'success']);
      } else {
         echo json_encode(['status' => 'no_data']);
      }
   }


   public function syncEmployees()
   {
      $url = $_ENV['API_EMPLOYEES_URL'] ?? 'http://************:3001/api/services/app/HR201EmployeesV2/GetAllV2';

      $curl = curl_init($url);
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($curl, CURLOPT_HTTPHEADER, ['Accept: application/json']);
      $response = curl_exec($curl);

      if (curl_errno($curl)) {
         echo 'cURL Error: ' . curl_error($curl);
         curl_close($curl);
         return;
      }
      curl_close($curl);

      $result = json_decode($response, true);

      if (isset($result['result'])) {
         $apiCount = count($result['result']);
         $dbCount = $this->db->count_all('users');

         if ($apiCount === $dbCount) {
            echo json_encode(['status' => 'no_changes']);
            return;
         }

         foreach ($result['result'] as $employee) {
            $first_name = $this->capitalizeWords($employee['hR201Employee']['firstname'] ?? 'Unknown');
            $last_name  = $this->capitalizeWords($employee['hR201Employee']['lastname'] ?? 'Unknown');
            $email      = isset($employee['hR201Employee']['personalEmailAddress']) &&
               strtolower($employee['hR201Employee']['personalEmailAddress']) !== 'n/a'
               ? $employee['hR201Employee']['personalEmailAddress']
               : 'placeholder_' . strtolower($first_name . '_' . $last_name) . '@example.com';

            // Clean and validate department data
            $department = trim($employee['hR201BusinessUnitDisplayProperty'] ?? '');
            if (empty($department) || preg_match('/^\s*-+\s*$/', $department)) {
               $department = 'Unassigned'; // Default department for invalid data
            }

            $data = [
               'first_name'        => $first_name,
               'last_name'         => $last_name,
               'email'             => $email,
               'department'        => $department,
               'employee_status'   => 'New Employee',
               'image_url'         => '',
               'created_at'        => date('Y-m-d H:i:s'),
               'last_active'       => '',
               'employment_status' => $employee['contractAssign'] ?? 'N/A',
               'transfer_status'   => $employee['transferStatus'] ?? '0',
               'contract_type'     => 'None'
            ];

            if ($this->User_model->check_email_exists($email)) {
               $existing_user = $this->User_model->get_user_by_email($email);
               $data['role'] = $existing_user['role'];
               $data['admin_role'] = $existing_user['admin_role'];
               $this->User_model->update_user($email, $data);
               log_message('info', "Updated existing user: $email");
            } else {
               $data['role'] = 'user';
               $data['admin_role'] = '';
               $result = $this->User_model->insert_user($data);
               if ($result) {
                  log_message('info', "Inserted new user: $email with department: {$data['department']}");
               } else {
                  log_message('error', "Failed to insert user: $email - Department validation failed");
               }
            }
         }

         echo json_encode(['status' => 'success']);
      } else {
         echo "Failed to fetch employee data.";
      }
   }


   private function capitalizeWords($string)
   {
      return ucwords(strtolower(trim($string)));
   }

   public function getUsersCount()
   {
      $count = $this->db->count_all('users');
      echo json_encode(['users_count' => $count]);
   }



   public function filterEmployees()
   {
      $searchName = $this->input->post('searchName');
      $department = $this->input->post('department');
      $status = $this->input->post('status');
      $transferStatus = $this->input->post('transferStatus');
      $contractType = $this->input->post('contractType');

      $this->db->select('id, first_name, last_name, email, department, employee_status, role, created_at, last_active, employment_status, transfer_status, contract_type');

      // Apply filters
      if (!empty($searchName)) {
         $this->db->group_start()
            ->like('first_name', $searchName)
            ->or_like('last_name', $searchName)
            ->group_end();
      }
      if (!empty($department)) {
         $this->db->where('department', $department);
      }
      if (!empty($status)) {
         $this->db->where('employee_status', $status);
      }

      if ($transferStatus !== '') {
         // Check if transferStatus is 1 and map it to 'Transferred'
         if ($transferStatus == 1) {
            $this->db->where('transfer_status', 'Transferred');
         } else {
            // For other cases, handle null or other values as needed
            $this->db->where('transfer_status', $transferStatus === 'null' ? null : $transferStatus);
         }
      }

      if (!empty($contractType)) {
         $this->db->where('contract_type', $contractType);
      }

      $query = $this->db->get('users');
      $data = $query->result();

      // Generate the updated HTML
      $output = '';

      foreach ($data as $index => $row) {
         $full_name = ucwords(strtolower($row->last_name)) . ', ' . ucwords(strtolower($row->first_name)); // Combine last name and first name

         $output .= "<div class='user-list-item'>";
         $output .= "<div class='list-item-number'>" . ($index + 1) . "</div>";
         $output .= "<div class='list-item-number'>" . $full_name . "</div>"; // Display full name as "last_name, first_name"
         $output .= "<div class='list-item-number'>" . $row->email . "</div>";
         $output .= "<div class='list-item-number'>" . $row->department . "</div>";
         $output .= "<div class='list-item-number'>" . ($row->employment_status ?: 'Pending') . "</div>";
         $output .= "<div class='list-item-number'>" . ($row->transfer_status == 1 ? 'Transferred' : ($row->transfer_status ?: 'Pending Status')) . "</div>";
         $output .= "<div class='list-item-number status-new'>" . ($row->employee_status ?: 'New Employee') . "</div>";
         $output .= "</div>";
      }


      echo $output ?: '<p>No users found.</p>';
   }
}
